# SCS Agent

一个智能文档检索和问答系统，具备网页爬取、向量化存储、智能检索和MCP（Model Context Protocol）接口功能。

## 🚀 功能特性

- **🕷️ 智能爬虫**: 支持静态和动态网页爬取，具备反爬虫策略和速率控制
- **🔍 向量检索**: 基于语义相似度的文档检索，支持中英文混合搜索
- **🤖 智能问答**: 基于检索增强生成(RAG)的问答系统
- **🔌 MCP接口**: 标准化的模型上下文协议，易于AI模型集成
- **🌐 多接口支持**: REST API、CLI工具、Python SDK
- **📊 可视化管理**: Web界面管理爬取任务和文档

## 📋 系统要求

- Python 3.10+
- 4GB+ RAM (推荐8GB)
- 2GB+ 磁盘空间

## 🛠️ 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd scs-agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install -e .
```

### 基本使用

#### 1. 命令行使用

```bash
# 爬取网页
scs-agent crawl https://example.com

# 搜索文档
scs-agent search "机器学习"

# 问答
scs-agent ask "什么是深度学习？"

# 启动Web服务
scs-agent serve --port 8000
```

#### 2. Python API

```python
from scs_agent import SCPAgent

# 初始化
agent = SCPAgent()

# 爬取URL
await agent.crawl_urls(["https://example.com"])

# 搜索
results = await agent.search("查询内容")

# 问答
answer = await agent.ask("问题")
```

#### 3. REST API

```bash
# 启动服务
scs-agent serve

# 爬取URL
curl -X POST http://localhost:8000/api/v1/crawl \
  -H "Content-Type: application/json" \
  -d '{"urls": ["https://example.com"]}'

# 搜索文档
curl -X POST http://localhost:8000/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"query": "机器学习", "limit": 10}'
```

## 📚 文档

- [架构设计](docs/architecture.md) - 系统架构和技术选型
- [实现指南](docs/implementation_guide.md) - 详细实现说明
- [API参考](docs/api_reference.md) - 完整API文档

## 🏗️ 项目结构

```
scs-agent/
├── scs_agent/                 # 主要代码
│   ├── core/                  # 核心引擎
│   ├── crawler/               # 爬虫模块
│   ├── processor/             # 内容处理
│   ├── vector_db/             # 向量数据库
│   ├── mcp/                   # MCP接口
│   ├── interfaces/            # 用户接口
│   └── utils/                 # 工具模块
├── docs/                      # 文档
├── tests/                     # 测试
└── examples/                  # 示例代码
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_crawler.py

# 生成覆盖率报告
pytest --cov=scs_agent --cov-report=html
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t scs-agent .

# 运行容器
docker run -p 8000:8000 -v $(pwd)/data:/app/data scs-agent

# 使用Docker Compose
docker-compose up -d
```

## ⚙️ 配置

### 环境变量

```bash
# 向量数据库配置
export VECTOR_DB__PERSIST_DIRECTORY="./chroma_db"
export VECTOR_DB__EMBEDDING_MODEL="all-MiniLM-L6-v2"

# 爬虫配置
export CRAWLER__MAX_CONCURRENT_REQUESTS=10
export CRAWLER__REQUEST_DELAY=1.0

# MCP配置
export MCP__HOST="0.0.0.0"
export MCP__PORT=8000
```

### 配置文件

```yaml
# config.yaml
crawler:
  max_concurrent_requests: 10
  request_delay: 1.0
  timeout: 30

vector_db:
  persist_directory: "./chroma_db"
  embedding_model: "all-MiniLM-L6-v2"
  chunk_size: 1000

mcp:
  host: "localhost"
  port: 8000
```

## 🤝 如何贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范

- 遵循 PEP 8 代码风格
- 添加适当的测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

### 维护者
- **cuiyi01** - <EMAIL>

### 讨论
- 百度Hi交流群：[群号]
- Issues: [项目Issues页面]

## 🔗 相关链接

- [更新日志](CHANGELOG.md)
- [项目主页](http://icode.baidu.com/repos/baidu/personal-code/scs-agent/tree/master)
- [在线文档](docs/)

## ⭐ 致谢

感谢以下开源项目：
- [ChromaDB](https://github.com/chroma-core/chroma) - 向量数据库
- [sentence-transformers](https://github.com/UKPLab/sentence-transformers) - 文本嵌入
- [FastAPI](https://github.com/tiangolo/fastapi) - Web框架
- [aiohttp](https://github.com/aio-libs/aiohttp) - 异步HTTP客户端
