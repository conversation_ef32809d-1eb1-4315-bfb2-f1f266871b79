# SCS Agent 架构设计文档

## 项目概述

SCS Agent 是一个智能文档检索和问答系统，具备网页爬取、向量化存储、智能检索和MCP（Model Context Protocol）接口功能。

## 系统目标

1. **爬虫功能**: 能够爬取指定链接列表的网页内容
2. **向量化存储**: 将爬取的内容进行向量化，存储在向量数据库中
3. **智能问答**: 基于用户意图检索向量数据库，提供相关回答和文档内容
4. **MCP接口**: 提供标准化的模型上下文协议入口

## 系统架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Server    │    │   Web Interface │    │   CLI Interface │
│                 │    │                 │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Core Engine          │
                    │  ┌─────────────────────┐  │
                    │  │   Query Processor   │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │  Retrieval Engine   │  │
                    │  └─────────────────────┘  │
                    │  ┌─────────────────────┐  │
                    │  │  Response Generator │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
│  Crawler Module │    │ Vector Database │    │ Content Processor│
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ URL Manager │ │    │ │   Chroma    │ │    │ │Text Extractor│ │
│ └─────────────┘ │    │ │             │ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ └─────────────┘ │    │ ┌─────────────┐ │
│ │Web Scraper  │ │    │ ┌─────────────┐ │    │ │Text Chunker │ │
│ └─────────────┘ │    │ │Embedding API│ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ │             │ │    │ ┌─────────────┐ │
│ │Rate Limiter │ │    │ └─────────────┘ │    │ │Text Cleaner │ │
│ └─────────────┘ │    └─────────────────┘    │ └─────────────┘ │
└─────────────────┘                           └─────────────────┘
```

## 模块分割

### 1. 核心模块 (scs_agent/core/)

#### 1.1 查询处理器 (query_processor.py)
- 用户查询解析和预处理
- 意图识别和查询优化
- 查询路由和分发

#### 1.2 检索引擎 (retrieval_engine.py)
- 向量相似度搜索
- 混合检索策略（向量+关键词）
- 结果排序和过滤

#### 1.3 响应生成器 (response_generator.py)
- 基于检索结果生成回答
- 上下文整合和格式化
- 引用和来源标注

### 2. 爬虫模块 (scs_agent/crawler/)

#### 2.1 URL管理器 (url_manager.py)
- URL队列管理
- 去重和优先级控制
- 爬取状态跟踪

#### 2.2 网页抓取器 (web_scraper.py)
- HTTP请求处理
- 反爬虫策略
- 错误重试机制

#### 2.3 速率限制器 (rate_limiter.py)
- 请求频率控制
- 并发数量管理
- 域名级别限制

### 3. 内容处理模块 (scs_agent/processor/)

#### 3.1 文本提取器 (text_extractor.py)
- HTML内容解析
- 主要内容识别
- 多媒体内容处理

#### 3.2 文本分块器 (text_chunker.py)
- 智能文本分割
- 重叠窗口处理
- 语义边界保持

#### 3.3 文本清理器 (text_cleaner.py)
- 噪声数据过滤
- 格式标准化
- 编码处理

### 4. 向量数据库模块 (scs_agent/vector_db/)

#### 4.1 数据库接口 (database.py)
- 向量存储和检索
- 元数据管理
- 索引优化

#### 4.2 嵌入服务 (embedding.py)
- 文本向量化
- 模型管理
- 批处理优化

### 5. MCP模块 (scs_agent/mcp/)

#### 5.1 MCP服务器 (server.py)
- 协议实现
- 消息处理
- 会话管理

#### 5.2 工具定义 (tools.py)
- 搜索工具
- 爬取工具
- 管理工具

### 6. 接口模块 (scs_agent/interfaces/)

#### 6.1 命令行接口 (cli.py)
- 命令解析
- 交互式模式
- 批处理模式

#### 6.2 Web接口 (web.py)
- REST API
- WebSocket支持
- 前端集成

### 7. 配置和工具模块 (scs_agent/utils/)

#### 7.1 配置管理 (config.py)
- 配置文件解析
- 环境变量处理
- 默认值管理

#### 7.2 日志系统 (logging.py)
- 结构化日志
- 多级别输出
- 性能监控

#### 7.3 数据模型 (models.py)
- 数据结构定义
- 序列化支持
- 验证逻辑

## 技术选型

### 核心技术栈

#### 编程语言
- **Python 3.10+**: 主要开发语言，丰富的生态系统

#### Web爬虫
- **aiohttp**: 异步HTTP客户端，高性能网络请求
- **BeautifulSoup4**: HTML解析和内容提取
- **Playwright**: 动态网页渲染和JavaScript执行
- **scrapy**: 可选的高级爬虫框架

#### 向量数据库
- **ChromaDB**: 轻量级向量数据库，易于部署和使用
- **备选方案**: Qdrant, Weaviate, Pinecone

#### 文本处理
- **spaCy**: 自然语言处理和文本分析
- **tiktoken**: 文本分词和长度计算
- **langdetect**: 语言检测

#### 嵌入模型
- **sentence-transformers**: 多语言文本嵌入
- **OpenAI Embeddings**: 高质量商业嵌入服务
- **本地模型**: BGE, M3E等中文优化模型

#### MCP协议
- **mcp**: Model Context Protocol官方实现
- **pydantic**: 数据验证和序列化

#### Web框架
- **FastAPI**: 现代异步Web框架
- **uvicorn**: ASGI服务器

#### 数据存储
- **SQLite**: 轻量级关系数据库，存储元数据
- **Redis**: 缓存和队列管理（可选）

#### 配置和部署
- **pydantic-settings**: 配置管理
- **docker**: 容器化部署
- **pytest**: 单元测试框架

### 依赖管理

主要依赖包：
```
aiohttp>=3.9.0
beautifulsoup4>=4.12.0
chromadb>=0.4.0
fastapi>=0.104.0
sentence-transformers>=2.2.0
spacy>=3.7.0
pydantic>=2.5.0
uvicorn>=0.24.0
playwright>=1.40.0
tiktoken>=0.5.0
```

开发依赖：
```
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.7.0
pre-commit>=3.5.0
```

## 部署架构

### 单机部署
- 所有组件运行在同一服务器
- 适合小规模使用和开发测试

### 分布式部署
- 爬虫集群：多个爬虫节点并行工作
- 向量数据库集群：支持水平扩展
- API网关：负载均衡和请求路由

### 云原生部署
- Kubernetes编排
- 微服务架构
- 自动扩缩容

## 性能指标

### 爬虫性能
- 并发请求数：100-1000/秒
- 页面处理速度：10-50页/秒
- 错误重试机制：指数退避

### 检索性能
- 查询响应时间：<100ms
- 向量检索QPS：1000+
- 准确率：>85%

### 系统可用性
- 服务可用性：99.9%
- 数据一致性：强一致性
- 故障恢复时间：<5分钟

## 扩展性设计

### 插件化架构
- 爬虫插件：支持不同网站的定制化爬取
- 处理插件：支持多种文档格式
- 嵌入插件：支持多种嵌入模型

### API扩展
- RESTful API：标准化接口
- GraphQL：灵活查询
- WebSocket：实时通信

### 多语言支持
- 中英文优化
- 多语言检测
- 跨语言检索
