# SCS Agent API 参考文档

## 概述

SCS Agent 提供多种接口方式：
1. **REST API**: HTTP接口，适合Web集成
2. **MCP接口**: Model Context Protocol，适合AI模型集成
3. **CLI接口**: 命令行工具，适合脚本和自动化
4. **Python API**: 直接调用，适合Python项目集成

## REST API

### 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **认证**: Bear<PERSON> (可选)
- **内容类型**: `application/json`

### 端点列表

#### 1. 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "version": "0.1.0",
  "timestamp": "2025-08-13T10:30:00Z"
}
```

#### 2. 爬取URL
```http
POST /crawl
```

**请求体**:
```json
{
  "urls": [
    "https://example.com",
    "https://example.org/page1"
  ],
  "options": {
    "max_depth": 2,
    "follow_links": true,
    "respect_robots": true,
    "delay": 1.0
  }
}
```

**响应示例**:
```json
{
  "task_id": "crawl_123456",
  "status": "started",
  "urls_count": 2,
  "estimated_time": "5 minutes"
}
```

#### 3. 查询爬取状态
```http
GET /crawl/{task_id}/status
```

**响应示例**:
```json
{
  "task_id": "crawl_123456",
  "status": "completed",
  "progress": {
    "total_urls": 2,
    "completed_urls": 2,
    "failed_urls": 0,
    "documents_created": 15
  },
  "started_at": "2025-08-13T10:30:00Z",
  "completed_at": "2025-08-13T10:35:00Z"
}
```

#### 4. 搜索文档
```http
POST /search
```

**请求体**:
```json
{
  "query": "机器学习算法",
  "limit": 10,
  "filters": {
    "source_domain": "example.com",
    "date_range": {
      "start": "2025-01-01",
      "end": "2025-12-31"
    }
  },
  "search_type": "hybrid"
}
```

**响应示例**:
```json
{
  "query": "机器学习算法",
  "results": [
    {
      "id": "doc_123",
      "title": "机器学习基础算法介绍",
      "content": "机器学习是人工智能的一个重要分支...",
      "url": "https://example.com/ml-basics",
      "score": 0.95,
      "metadata": {
        "source": "example.com",
        "crawled_at": "2025-08-13T10:30:00Z",
        "content_type": "article"
      }
    }
  ],
  "total_results": 25,
  "search_time_ms": 45
}
```

#### 5. 问答接口
```http
POST /qa
```

**请求体**:
```json
{
  "question": "什么是深度学习？",
  "context_limit": 5,
  "include_sources": true,
  "language": "zh"
}
```

**响应示例**:
```json
{
  "question": "什么是深度学习？",
  "answer": "深度学习是机器学习的一个子领域，它使用多层神经网络来学习数据的复杂模式...",
  "sources": [
    {
      "title": "深度学习入门",
      "url": "https://example.com/deep-learning",
      "relevance_score": 0.92
    }
  ],
  "confidence": 0.88,
  "response_time_ms": 120
}
```

#### 6. 文档管理
```http
GET /documents
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 20)
- `source`: 来源过滤
- `search`: 内容搜索

**响应示例**:
```json
{
  "documents": [
    {
      "id": "doc_123",
      "title": "文档标题",
      "url": "https://example.com/page",
      "content_preview": "文档内容预览...",
      "metadata": {
        "source": "example.com",
        "crawled_at": "2025-08-13T10:30:00Z"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "size": 20,
    "total": 100,
    "pages": 5
  }
}
```

```http
DELETE /documents/{doc_id}
```

**响应示例**:
```json
{
  "message": "Document deleted successfully",
  "document_id": "doc_123"
}
```

## MCP接口

### 工具列表

#### 1. search_documents
搜索文档内容

**输入参数**:
```json
{
  "query": "搜索查询字符串",
  "limit": 10,
  "filters": {}
}
```

**输出**:
```json
{
  "results": [
    {
      "title": "文档标题",
      "content": "相关内容片段",
      "url": "来源URL",
      "score": 0.95
    }
  ]
}
```

#### 2. crawl_urls
爬取指定URL列表

**输入参数**:
```json
{
  "urls": ["https://example.com"],
  "options": {
    "max_depth": 1,
    "follow_links": false
  }
}
```

**输出**:
```json
{
  "task_id": "crawl_123456",
  "status": "started",
  "message": "爬取任务已启动"
}
```

#### 3. get_crawl_status
获取爬取任务状态

**输入参数**:
```json
{
  "task_id": "crawl_123456"
}
```

**输出**:
```json
{
  "status": "completed",
  "progress": {
    "completed": 10,
    "total": 10
  }
}
```

#### 4. ask_question
基于文档内容回答问题

**输入参数**:
```json
{
  "question": "问题内容",
  "context_limit": 5
}
```

**输出**:
```json
{
  "answer": "基于文档内容的回答",
  "sources": ["相关文档来源"],
  "confidence": 0.88
}
```

## CLI接口

### 基本命令

#### 爬取命令
```bash
# 爬取单个URL
scs-agent crawl https://example.com

# 爬取多个URL
scs-agent crawl https://example.com https://example.org

# 从文件读取URL列表
scs-agent crawl --from-file urls.txt

# 设置爬取选项
scs-agent crawl https://example.com --max-depth 2 --delay 1.5
```

#### 搜索命令
```bash
# 基本搜索
scs-agent search "机器学习"

# 限制结果数量
scs-agent search "深度学习" --limit 5

# 指定输出格式
scs-agent search "AI" --format json
```

#### 问答命令
```bash
# 基本问答
scs-agent ask "什么是人工智能？"

# 包含来源信息
scs-agent ask "深度学习的应用" --include-sources

# 指定语言
scs-agent ask "What is machine learning?" --language en
```

#### 管理命令
```bash
# 查看文档统计
scs-agent stats

# 清理数据库
scs-agent clean --confirm

# 导出数据
scs-agent export --format json --output data.json

# 启动Web服务
scs-agent serve --host 0.0.0.0 --port 8000
```

### 配置选项

#### 全局配置
```bash
# 设置配置
scs-agent config set vector_db.embedding_model "sentence-transformers/all-MiniLM-L6-v2"

# 查看配置
scs-agent config get

# 重置配置
scs-agent config reset
```

## Python API

### 基本使用

```python
from scs_agent import SCPAgent

# 初始化
agent = SCPAgent()

# 爬取URL
await agent.crawl_urls([
    "https://example.com",
    "https://example.org"
])

# 搜索文档
results = await agent.search("机器学习", limit=10)

# 问答
answer = await agent.ask("什么是深度学习？")

# 获取文档
documents = await agent.get_documents(page=1, size=20)
```

### 高级配置

```python
from scs_agent import SCPAgent, Config

# 自定义配置
config = Config(
    crawler=CrawlerConfig(
        max_concurrent_requests=20,
        request_delay=0.5
    ),
    vector_db=VectorDBConfig(
        embedding_model="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        chunk_size=800
    )
)

agent = SCPAgent(config=config)
```

### 异步上下文管理

```python
async with SCPAgent() as agent:
    # 批量操作
    tasks = [
        agent.crawl_urls(["https://site1.com"]),
        agent.crawl_urls(["https://site2.com"])
    ]
    await asyncio.gather(*tasks)
    
    # 搜索
    results = await agent.search("查询内容")
```

## 错误处理

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数 'query' 不能为空",
    "details": {
      "parameter": "query",
      "expected": "non-empty string"
    }
  },
  "request_id": "req_123456"
}
```

### 常见错误代码
- `INVALID_PARAMETER`: 参数错误
- `RESOURCE_NOT_FOUND`: 资源不存在
- `RATE_LIMIT_EXCEEDED`: 频率限制
- `CRAWL_FAILED`: 爬取失败
- `SEARCH_TIMEOUT`: 搜索超时
- `EMBEDDING_ERROR`: 向量化错误

## 限制和配额

### 默认限制
- 并发爬取请求: 10个
- 搜索结果上限: 100条
- 问答上下文长度: 4000 tokens
- 文档大小上限: 10MB
- API请求频率: 100次/分钟

### 配置调整
可通过配置文件或环境变量调整这些限制：

```yaml
# config.yaml
limits:
  max_concurrent_crawls: 20
  max_search_results: 200
  max_context_tokens: 8000
  max_document_size: 20971520  # 20MB
  api_rate_limit: 200  # per minute
```
