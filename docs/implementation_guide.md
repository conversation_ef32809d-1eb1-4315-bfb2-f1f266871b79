# SCS Agent 实现指南

## 开发环境设置

### 环境要求
- Python 3.10+
- Git
- Docker (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd scs-agent
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
pip install -e .
```

## 核心模块实现

### 1. 爬虫模块实现

#### URL管理器
```python
# scs_agent/crawler/url_manager.py
from typing import Set, List, Optional
from dataclasses import dataclass
from enum import Enum
import asyncio
from urllib.parse import urljoin, urlparse

class URLStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class URLItem:
    url: str
    priority: int = 0
    retry_count: int = 0
    status: URLStatus = URLStatus.PENDING
    metadata: dict = None
```

#### 网页抓取器
```python
# scs_agent/crawler/web_scraper.py
import aiohttp
import asyncio
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from typing import Optional, Dict, Any

class WebScraper:
    def __init__(self, config: dict):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def fetch_static(self, url: str) -> Dict[str, Any]:
        """抓取静态页面"""
        async with self.session.get(url) as response:
            content = await response.text()
            return {
                'url': url,
                'content': content,
                'status_code': response.status,
                'headers': dict(response.headers)
            }
    
    async def fetch_dynamic(self, url: str) -> Dict[str, Any]:
        """抓取动态页面（需要JavaScript渲染）"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.goto(url)
            content = await page.content()
            await browser.close()
            return {
                'url': url,
                'content': content,
                'status_code': 200
            }
```

### 2. 内容处理模块实现

#### 文本提取器
```python
# scs_agent/processor/text_extractor.py
from bs4 import BeautifulSoup
import re
from typing import Dict, List

class TextExtractor:
    def __init__(self):
        self.noise_patterns = [
            r'<script.*?</script>',
            r'<style.*?</style>',
            r'<!--.*?-->',
        ]
    
    def extract_from_html(self, html: str) -> Dict[str, str]:
        """从HTML中提取主要内容"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除噪声标签
        for script in soup(["script", "style", "nav", "footer"]):
            script.decompose()
        
        # 提取标题
        title = soup.find('title')
        title_text = title.get_text().strip() if title else ""
        
        # 提取主要内容
        main_content = self._extract_main_content(soup)
        
        return {
            'title': title_text,
            'content': main_content,
            'url': '',
            'metadata': self._extract_metadata(soup)
        }
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """提取主要内容"""
        # 优先查找主要内容区域
        main_selectors = [
            'main', 'article', '.content', '#content',
            '.main-content', '.post-content'
        ]
        
        for selector in main_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(separator=' ', strip=True)
        
        # 如果没有找到主要内容区域，返回body内容
        body = soup.find('body')
        return body.get_text(separator=' ', strip=True) if body else ""
```

#### 文本分块器
```python
# scs_agent/processor/text_chunker.py
import tiktoken
from typing import List, Dict

class TextChunker:
    def __init__(self, chunk_size: int = 1000, overlap: int = 200):
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def chunk_text(self, text: str, metadata: Dict = None) -> List[Dict]:
        """将文本分割成块"""
        # 按段落分割
        paragraphs = text.split('\n\n')
        chunks = []
        current_chunk = ""
        current_tokens = 0
        
        for paragraph in paragraphs:
            paragraph_tokens = len(self.encoding.encode(paragraph))
            
            if current_tokens + paragraph_tokens > self.chunk_size:
                if current_chunk:
                    chunks.append(self._create_chunk(current_chunk, metadata))
                    # 保留重叠部分
                    current_chunk = self._get_overlap_text(current_chunk)
                    current_tokens = len(self.encoding.encode(current_chunk))
                
                # 如果单个段落太长，需要进一步分割
                if paragraph_tokens > self.chunk_size:
                    sub_chunks = self._split_long_paragraph(paragraph)
                    chunks.extend([self._create_chunk(chunk, metadata) for chunk in sub_chunks])
                    current_chunk = ""
                    current_tokens = 0
                else:
                    current_chunk += paragraph + "\n\n"
                    current_tokens += paragraph_tokens
            else:
                current_chunk += paragraph + "\n\n"
                current_tokens += paragraph_tokens
        
        if current_chunk.strip():
            chunks.append(self._create_chunk(current_chunk, metadata))
        
        return chunks
    
    def _create_chunk(self, text: str, metadata: Dict = None) -> Dict:
        """创建文本块"""
        return {
            'text': text.strip(),
            'metadata': metadata or {},
            'token_count': len(self.encoding.encode(text))
        }
```

### 3. 向量数据库模块实现

#### 数据库接口
```python
# scs_agent/vector_db/database.py
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional
import uuid

class VectorDatabase:
    def __init__(self, persist_directory: str = "./chroma_db"):
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )
        self.collection = self.client.get_or_create_collection(
            name="documents",
            metadata={"hnsw:space": "cosine"}
        )
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> List[str]:
        """添加文档到向量数据库"""
        ids = []
        texts = []
        metadatas = []
        
        for doc in documents:
            doc_id = str(uuid.uuid4())
            ids.append(doc_id)
            texts.append(doc['text'])
            metadatas.append(doc.get('metadata', {}))
        
        self.collection.add(
            documents=texts,
            metadatas=metadatas,
            ids=ids
        )
        
        return ids
    
    async def search(self, query: str, n_results: int = 10) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results,
            include=['documents', 'metadatas', 'distances']
        )
        
        documents = []
        for i in range(len(results['documents'][0])):
            documents.append({
                'id': results['ids'][0][i],
                'text': results['documents'][0][i],
                'metadata': results['metadatas'][0][i],
                'score': 1 - results['distances'][0][i]  # 转换为相似度分数
            })
        
        return documents
```

#### 嵌入服务
```python
# scs_agent/vector_db/embedding.py
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Union

class EmbeddingService:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
    
    def encode(self, texts: Union[str, List[str]]) -> np.ndarray:
        """将文本编码为向量"""
        if isinstance(texts, str):
            texts = [texts]
        
        embeddings = self.model.encode(texts, convert_to_numpy=True)
        return embeddings
    
    def encode_batch(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """批量编码文本"""
        embeddings = self.model.encode(
            texts, 
            batch_size=batch_size,
            convert_to_numpy=True,
            show_progress_bar=True
        )
        return embeddings
```

### 4. MCP模块实现

#### MCP服务器
```python
# scs_agent/mcp/server.py
from mcp.server import Server
from mcp.types import Tool, TextContent
import asyncio
from typing import Any, Sequence

class SCPMCPServer:
    def __init__(self, core_engine):
        self.server = Server("scs-agent")
        self.core_engine = core_engine
        self._setup_tools()
    
    def _setup_tools(self):
        """设置MCP工具"""
        
        @self.server.list_tools()
        async def list_tools() -> list[Tool]:
            return [
                Tool(
                    name="search_documents",
                    description="搜索文档内容",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询"},
                            "limit": {"type": "integer", "description": "结果数量限制", "default": 10}
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="crawl_urls",
                    description="爬取指定URL列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "urls": {"type": "array", "items": {"type": "string"}},
                            "options": {"type": "object", "description": "爬取选项"}
                        },
                        "required": ["urls"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: dict) -> Sequence[TextContent]:
            if name == "search_documents":
                results = await self.core_engine.search(
                    arguments["query"], 
                    limit=arguments.get("limit", 10)
                )
                return [TextContent(type="text", text=str(results))]
            
            elif name == "crawl_urls":
                await self.core_engine.crawl_urls(
                    arguments["urls"],
                    options=arguments.get("options", {})
                )
                return [TextContent(type="text", text="爬取任务已启动")]
            
            else:
                raise ValueError(f"Unknown tool: {name}")
    
    async def run(self, transport):
        """运行MCP服务器"""
        await self.server.run(transport)
```

## 配置管理

### 配置文件结构
```python
# scs_agent/utils/config.py
from pydantic_settings import BaseSettings
from typing import List, Optional

class CrawlerConfig(BaseSettings):
    max_concurrent_requests: int = 10
    request_delay: float = 1.0
    timeout: int = 30
    user_agent: str = "SCS-Agent/1.0"
    
class VectorDBConfig(BaseSettings):
    persist_directory: str = "./chroma_db"
    embedding_model: str = "all-MiniLM-L6-v2"
    chunk_size: int = 1000
    chunk_overlap: int = 200

class MCPConfig(BaseSettings):
    host: str = "localhost"
    port: int = 8000
    
class Config(BaseSettings):
    crawler: CrawlerConfig = CrawlerConfig()
    vector_db: VectorDBConfig = VectorDBConfig()
    mcp: MCPConfig = MCPConfig()
    
    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"
```

## 测试策略

### 单元测试
```python
# tests/test_text_extractor.py
import pytest
from scs_agent.processor.text_extractor import TextExtractor

class TestTextExtractor:
    def setup_method(self):
        self.extractor = TextExtractor()
    
    def test_extract_from_html(self):
        html = """
        <html>
            <head><title>Test Page</title></head>
            <body>
                <main>
                    <h1>Main Title</h1>
                    <p>This is the main content.</p>
                </main>
                <script>console.log('noise');</script>
            </body>
        </html>
        """
        
        result = self.extractor.extract_from_html(html)
        
        assert result['title'] == "Test Page"
        assert "Main Title" in result['content']
        assert "This is the main content." in result['content']
        assert "console.log" not in result['content']
```

### 集成测试
```python
# tests/test_integration.py
import pytest
import asyncio
from scs_agent.core.engine import CoreEngine

@pytest.mark.asyncio
class TestIntegration:
    async def test_crawl_and_search_workflow(self):
        engine = CoreEngine()
        
        # 爬取测试URL
        urls = ["https://example.com"]
        await engine.crawl_urls(urls)
        
        # 搜索内容
        results = await engine.search("example")
        
        assert len(results) > 0
        assert any("example" in result['text'].lower() for result in results)
```

## 部署指南

### Docker部署
```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN pip install -e .

EXPOSE 8000

CMD ["python", "-m", "scs_agent.mcp.server"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  scs-agent:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./chroma_db:/app/chroma_db
    environment:
      - VECTOR_DB__PERSIST_DIRECTORY=/app/chroma_db
      - MCP__HOST=0.0.0.0
```

## 性能优化

### 异步处理
- 使用asyncio进行并发爬取
- 异步向量化处理
- 连接池管理

### 缓存策略
- Redis缓存热点查询
- 本地缓存嵌入结果
- 页面内容缓存

### 监控和日志
- 结构化日志记录
- 性能指标收集
- 错误追踪和报警
