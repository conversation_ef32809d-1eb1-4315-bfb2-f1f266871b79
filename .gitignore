# Virtualenv
/.venv/
/venv/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
/bin/
/build/
/develop-eggs/
/dist/
/eggs/
/lib/
/lib64/
/output/
/parts/
/sdist/
/var/
/*.egg-info/
/.installed.cfg
/*.egg
/.eggs

# AUTHORS and ChangeLog will be generated while packaging
/AUTHORS
/ChangeLog

# BCloud / BuildSubmitter
/build_submitter.*
/logger_client_log

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
.tox/
.coverage
.cache
.pytest_cache
nosetests.xml
coverage.xml

# Translations
*.mo

# Sphinx documentation
/docs/_build/

